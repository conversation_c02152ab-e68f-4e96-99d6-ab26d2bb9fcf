// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 1000,
    easing: 'ease-in-out',
    once: true,
    mirror: false
});

// Typing Animation
document.addEventListener('DOMContentLoaded', function() {
    const typedTextElement = document.getElementById('typed-text');
    if (typedTextElement) {
        const textToType = "तुमचं स्वतःचं ॲप किंवा वेबसाईट सुरु करायचंय?";
        let index = 0;

        function typeText() {
            if (index < textToType.length) {
                typedTextElement.textContent += textToType.charAt(index);
                index++;
                setTimeout(typeText, 100); // Adjust speed here (100ms per character)
            }
        }

        // Start typing after a short delay
        setTimeout(typeText, 500);
    }
});

// Mobile Navigation Toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-menu a').forEach(link => {
    link.addEventListener('click', () => {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
    });
});

// Header scroll effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.98)';
        header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
    } else {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
    }
});

// FAQ Accordion
document.querySelectorAll('.faq-question').forEach(question => {
    question.addEventListener('click', () => {
        const faqItem = question.parentElement;
        const isActive = faqItem.classList.contains('active');
        
        // Close all FAQ items
        document.querySelectorAll('.faq-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Open clicked item if it wasn't active
        if (!isActive) {
            faqItem.classList.add('active');
        }
    });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = target.offsetTop - headerHeight;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    });
});

// Contact Form Handling with Formspree
const contactForm = document.querySelector('.contact-form');
if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate form first
        if (!validateForm(this)) {
            return;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> पाठवत आहे...';
        submitBtn.disabled = true;

        // Get form data
        const formData = new FormData(this);

        // Submit to Formspree
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'Accept': 'application/json'
            }
        })
        .then(response => {
            if (response.ok) {
                // Show success message
                showNotification('तुमचा संदेश यशस्वीरित्या पाठवला गेला! आम्ही लवकरच तुमच्याशी संपर्क साधू.', 'success');

                // Reset form
                this.reset();

                // Also create WhatsApp message as backup
                const name = formData.get('name');
                const phone = formData.get('phone');
                const email = formData.get('email');
                const service = formData.get('service');
                const message = formData.get('message');

                const whatsappMessage = `नमस्कार! मी ${name} आहे.\n\nमाझी संपर्क माहिती:\n📞 फोन: ${phone}\n📧 ईमेल: ${email}\n\nमला ${getServiceName(service)} ची गरज आहे.\n\nसंदेश: ${message}\n\nकृपया माझ्याशी संपर्क साधा. धन्यवाद!`;

                // Show option to also send via WhatsApp
                setTimeout(() => {
                    if (confirm('तुमचा संदेश ईमेलने पाठवला गेला आहे. व्हाट्सॲपवर पण पाठवायचा आहे का?')) {
                        const encodedMessage = encodeURIComponent(whatsappMessage);
                        window.open(`https://wa.me/919604069989?text=${encodedMessage}`, '_blank');
                    }
                }, 2000);

            } else {
                throw new Error('Form submission failed');
            }
        })
        .catch(error => {
            console.error('Form submission error:', error);
            showNotification('संदेश पाठवण्यात अडचण आली. कृपया पुन्हा प्रयत्न करा किंवा थेट कॉल करा.', 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
}

// Helper function to get service name in Marathi
function getServiceName(serviceValue) {
    const services = {
        'website': 'वेबसाईट बनवायची',
        'app': 'मोबाईल ॲप बनवायचं',
        'ecommerce': 'ऑनलाईन दुकान सुरू करायचं',
        'seo': 'डिजिटल मार्केटिंग',
        'software': 'कस्टम सॉफ्टवेअर',
        'other': 'इतर सेवा'
    };
    return services[serviceValue] || serviceValue;
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#3b82f6'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
        font-family: 'Noto Sans Devanagari', sans-serif;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    });
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

// Typing animation for hero text
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// Initialize typing animation when page loads
window.addEventListener('load', () => {
    const heroTitle = document.querySelector('.hero-content h1');
    if (heroTitle) {
        const originalText = heroTitle.textContent;
        setTimeout(() => {
            typeWriter(heroTitle, originalText, 50);
        }, 1000);
    }
});

// Counter animation for stats (if you want to add stats section later)
function animateCounter(element, target, duration = 2000) {
    let start = 0;
    const increment = target / (duration / 16);
    
    function updateCounter() {
        start += increment;
        if (start < target) {
            element.textContent = Math.floor(start);
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = target;
        }
    }
    
    updateCounter();
}

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('loaded');
        }
    });
}, observerOptions);

// Observe all elements with loading class
document.querySelectorAll('.loading').forEach(el => {
    observer.observe(el);
});

// Removed parallax effect for hero image as requested

// Service card hover effects
document.querySelectorAll('.service-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});

// Portfolio item click handling
document.querySelectorAll('.portfolio-item').forEach(item => {
    item.addEventListener('click', function() {
        const title = this.querySelector('h3').textContent;
        showNotification(`${title} बद्दल अधिक माहितीसाठी आमच्याशी संपर्क साधा!`, 'info');
    });
});

// Quick action buttons analytics (you can integrate with Google Analytics)
document.querySelectorAll('.quick-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const action = this.getAttribute('title');
        console.log(`Quick action clicked: ${action}`);
        // You can add Google Analytics tracking here
        // gtag('event', 'click', { 'event_category': 'Quick Actions', 'event_label': action });
    });
});

// Lazy loading for images
const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
        }
    });
});

document.querySelectorAll('img[data-src]').forEach(img => {
    imageObserver.observe(img);
});

// Form validation
function validateForm(form) {
    const name = form.querySelector('#name').value.trim();
    const phone = form.querySelector('#phone').value.trim();
    const service = form.querySelector('#service').value;
    
    if (!name) {
        showNotification('कृपया तुमचं नाव टाका', 'error');
        return false;
    }
    
    if (!phone || phone.length < 10) {
        showNotification('कृपया योग्य मोबाईल नंबर टाका', 'error');
        return false;
    }
    
    if (!service) {
        showNotification('कृपया सेवा निवडा', 'error');
        return false;
    }
    
    return true;
}





// Console welcome message
console.log(`
🚀 रिया सॉफ्टवेअर वेबसाईट
📧 <EMAIL>
📞 +91 98765 43210

Made with ❤️ for Maharashtra's digital transformation!
`);

// Performance monitoring
window.addEventListener('load', () => {
    const loadTime = performance.now();
    console.log(`Page loaded in ${Math.round(loadTime)}ms`);
});

// Error handling
window.addEventListener('error', (e) => {
    console.error('JavaScript Error:', e.error);
});

// Service Worker registration (for PWA features - optional)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Popup Management
let offersPopupShown = false;
let announcementPopupShown = false;

// Show offers popup after 10 seconds
setTimeout(() => {
    if (!offersPopupShown && !localStorage.getItem('offersPopupClosed')) {
        showOffersPopup();
    }
}, 10000);

// Show announcement popup after 30 seconds
setTimeout(() => {
    if (!announcementPopupShown && !localStorage.getItem('announcementPopupClosed')) {
        showAnnouncementPopup();
    }
}, 30000);

// Show offers popup on scroll (50% of page)
window.addEventListener('scroll', () => {
    const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
    if (scrollPercent > 50 && !offersPopupShown && !localStorage.getItem('offersPopupClosed')) {
        showOffersPopup();
    }
});

function showOffersPopup() {
    const popup = document.getElementById('offersPopup');
    if (popup && !offersPopupShown) {
        popup.classList.add('show');
        offersPopupShown = true;
        startOfferTimer();

        // Close on background click
        popup.addEventListener('click', (e) => {
            if (e.target === popup) {
                closeOffersPopup();
            }
        });
    }
}

function closeOffersPopup() {
    const popup = document.getElementById('offersPopup');
    if (popup) {
        popup.classList.remove('show');
        localStorage.setItem('offersPopupClosed', 'true');
    }
}

function showAnnouncementPopup() {
    const popup = document.getElementById('announcementPopup');
    if (popup && !announcementPopupShown) {
        popup.classList.add('show');
        announcementPopupShown = true;

        // Close on background click
        popup.addEventListener('click', (e) => {
            if (e.target === popup) {
                closeAnnouncementPopup();
            }
        });
    }
}

function closeAnnouncementPopup() {
    const popup = document.getElementById('announcementPopup');
    if (popup) {
        popup.classList.remove('show');
        localStorage.setItem('announcementPopupClosed', 'true');
    }
}

// Offer timer countdown
function startOfferTimer() {
    const timerElement = document.getElementById('timer');
    if (!timerElement) return;

    let timeLeft = 24 * 60 * 60; // 24 hours in seconds

    const timer = setInterval(() => {
        const hours = Math.floor(timeLeft / 3600);
        const minutes = Math.floor((timeLeft % 3600) / 60);
        const seconds = timeLeft % 60;

        timerElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        timeLeft--;

        if (timeLeft < 0) {
            clearInterval(timer);
            timerElement.textContent = '00:00:00';
        }
    }, 1000);
}

// Reset popup visibility on new day
const today = new Date().toDateString();
if (localStorage.getItem('lastVisit') !== today) {
    localStorage.removeItem('offersPopupClosed');
    localStorage.removeItem('announcementPopupClosed');
    localStorage.setItem('lastVisit', today);
}

// Exit intent popup (desktop only)
if (!window.matchMedia('(max-width: 768px)').matches) {
    let exitIntentShown = false;

    document.addEventListener('mouseleave', (e) => {
        if (e.clientY <= 0 && !exitIntentShown && !localStorage.getItem('offersPopupClosed')) {
            showOffersPopup();
            exitIntentShown = true;
        }
    });
}
