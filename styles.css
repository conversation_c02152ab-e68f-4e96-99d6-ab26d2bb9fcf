/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #ff6b35;
    --secondary-color: #f7931e;
    --accent-color: #1e3a8a;
    --success-color: #10b981;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --white: #ffffff;
    --light-bg: #f9fafb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Noto Sans Devanagari', 'Hind', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-menu a:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: var(--white);
    padding-top: 80px;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    cursor: pointer;
}

.btn-primary {
    background: var(--white);
    color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--light-bg);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.hero-image {
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
}

/* Marquee Section */
.marquee-section {
    background: linear-gradient(135deg, var(--accent-color), #1e40af);
    color: var(--white);
    padding: 1rem 0;
    overflow: hidden;
    position: relative;
}

.marquee-container {
    width: 100%;
    overflow: hidden;
}

.marquee-content {
    display: flex;
    animation: marquee 30s linear infinite;
    white-space: nowrap;
}

.marquee-content span {
    padding: 0 3rem;
    font-size: 1.1rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    min-width: max-content;
}

@keyframes marquee {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

.marquee-section:hover .marquee-content {
    animation-play-state: paused;
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: var(--light-bg);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
    border: 1px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.service-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: var(--transition);
    margin-top: 1rem;
}

.service-link:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.service-link::after {
    content: '→';
    transition: var(--transition);
}

.service-link:hover::after {
    transform: translateX(3px);
}

/* Quick Actions */
.quick-actions {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    z-index: 999;
}

.quick-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--white);
    font-size: 1.5rem;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
}

.call-btn {
    background: var(--accent-color);
}

.whatsapp-btn {
    background: #25d366;
}

.website-btn {
    background: var(--primary-color);
}

.app-btn {
    background: var(--secondary-color);
}

.quick-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 25px -5px rgba(0, 0, 0, 0.2);
}

/* Portfolio Section */
.portfolio {
    padding: 5rem 0;
    background: var(--white);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
    gap: 2rem;
}

.portfolio-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.portfolio-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: 2rem;
    transform: translateY(100%);
    transition: var(--transition);
}

.portfolio-item:hover .portfolio-overlay {
    transform: translateY(0);
}

.portfolio-item:hover img {
    transform: scale(1.1);
}

.portfolio-overlay h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Testimonials Section */
.testimonials {
    padding: 5rem 0;
    background: var(--light-bg);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-content p {
    font-style: italic;
    color: var(--text-light);
    line-height: 1.6;
    position: relative;
}

.testimonial-content p::before {
    content: '"';
    font-size: 3rem;
    color: var(--primary-color);
    position: absolute;
    top: -10px;
    left: -10px;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-author h4 {
    font-weight: 600;
    color: var(--text-dark);
}

.testimonial-author span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* FAQ Section */
.faq {
    padding: 5rem 0;
    background: var(--white);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--white);
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: var(--transition);
}

.faq-item:hover {
    box-shadow: var(--shadow);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--light-bg);
    transition: var(--transition);
}

.faq-question:hover {
    background: #f3f4f6;
}

.faq-question h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
}

.faq-question i {
    color: var(--primary-color);
    transition: var(--transition);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: var(--light-bg);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
}

.contact-item h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.contact-item p {
    color: var(--text-light);
}

.contact-form {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

/* Mobile contact improvements */
@media (max-width: 767px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info {
        order: 2;
    }

    .contact-form {
        order: 1;
        margin: 0;
    }

    .contact-item {
        padding: 1rem;
    }

    .contact-item i {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Mobile-friendly footer improvements */
@media (max-width: 767px) {
    .footer-section ul li a {
        padding: 0.4rem 0;
        min-height: 44px;
        display: flex;
        align-items: center;
        border-radius: 4px;
        transition: var(--transition);
    }

    .footer-section ul li a:hover {
        background: rgba(255, 107, 53, 0.1);
        padding-left: 0.5rem;
    }

    .social-links a {
        min-height: 44px;
        min-width: 44px;
    }

    .footer-section .contact-info p {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.3rem 0;
        min-height: 44px;
    }

    .footer-section .contact-info i {
        flex-shrink: 0;
    }
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.footer-section p {
    color: #d1d5db;
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
    font-size: 0.85rem;
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.8rem;
    text-decoration: none;
    box-shadow: var(--shadow-lg);
    z-index: 999;
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    background: #128c7e;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Get Started Fixed Button */
.get-started-fixed {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 999;
}

.get-started-fixed .btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    box-shadow: var(--shadow-lg);
    animation: bounce 2s infinite;
}

.get-started-fixed .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 25px -5px rgba(255, 107, 53, 0.4);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Responsive Design */

/* Large Desktop Screens (1440px and above) */
@media (min-width: 1440px) {
    .container {
        max-width: 1400px;
    }

    .hero-content h1 {
        font-size: 3.5rem;
    }

    .hero-content h2 {
        font-size: 2.5rem;
    }

    .section-header h2 {
        font-size: 3rem;
    }

    .service-card {
        padding: 3rem;
    }

    .service-icon {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }
}

/* Desktop and Large Tablets (1024px to 1439px) */
@media (min-width: 1024px) and (max-width: 1439px) {
    .container {
        max-width: 1200px;
    }

    .hero-container {
        gap: 3rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .portfolio-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablets (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .container {
        padding: 0 30px;
    }

    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content h2 {
        font-size: 1.8rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem;
    }

    .portfolio-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .quick-actions {
        right: 15px;
    }

    .quick-btn {
        width: 55px;
        height: 55px;
        font-size: 1.3rem;
    }

    .marquee-content span {
        font-size: 1.05rem;
        padding: 0 2.5rem;
    }
}

/* Mobile Devices (up to 767px) */
@media (max-width: 767px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
        padding: 2rem 20px;
    }

    .hero-content h1 {
        font-size: 2rem;
        line-height: 1.3;
    }

    .hero-content h2 {
        font-size: 1.5rem;
        line-height: 1.4;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem 1.5rem;
    }

    .service-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .quick-actions {
        right: 10px;
        gap: 0.5rem;
    }

    .quick-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .get-started-fixed {
        bottom: 10px;
        right: 10px;
    }

    .whatsapp-float {
        bottom: 10px;
        left: 10px;
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .section-header p {
        font-size: 1.1rem;
    }

    .marquee-content span {
        font-size: 0.95rem;
        padding: 0 1.5rem;
    }
}

/* Large Mobile Devices (481px to 767px) */
@media (min-width: 481px) and (max-width: 767px) {
    .hero-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 1rem;
    }

    .hero-buttons .btn {
        flex: 1;
        max-width: 200px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-form {
        padding: 2rem;
    }

    .popup-content,
    .announcement-content {
        width: 85%;
    }
}

/* Standard Mobile Devices (376px to 480px) */
@media (min-width: 376px) and (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-content h1 {
        font-size: 1.8rem;
        line-height: 1.2;
    }

    .hero-content h2 {
        font-size: 1.4rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .service-card,
    .testimonial-card {
        padding: 1.5rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
    }

    .marquee-content span {
        font-size: 0.9rem;
        padding: 0 1rem;
    }

    .offer-item {
        padding: 1rem;
    }

    .popup-header,
    .announcement-header {
        padding: 1.5rem;
    }

    .popup-header h2,
    .announcement-header h2 {
        font-size: 1.5rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.loading {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    font-family: 'Noto Sans Devanagari', sans-serif;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    color: var(--white);
}

.notification-success .notification-content {
    background: var(--success-color);
}

.notification-error .notification-content {
    background: #ef4444;
}

.notification-info .notification-content {
    background: #3b82f6;
}

.notification-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    margin-left: auto;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Lazy Loading Images */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Print Styles */
@media print {
    .header,
    .quick-actions,
    .whatsapp-float,
    .get-started-fixed {
        display: none !important;
    }

    .hero {
        min-height: auto;
        padding: 2rem 0;
    }

    .section {
        page-break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #d97706;
        --secondary-color: #92400e;
        --text-dark: #000000;
        --text-light: #374151;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip to Content Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10001;
}

.skip-link:focus {
    top: 6px;
}

/* Additional Mobile Improvements */
@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .services-grid {
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem 1.5rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer {
        padding: 2rem 0 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-section {
        padding: 0 1rem;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
    }

    .footer-section p {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .footer-section ul li a {
        font-size: 0.85rem;
        padding: 0.2rem 0;
        display: block;
    }

    .social-links {
        justify-content: center;
        gap: 0.8rem;
        margin-top: 1.5rem;
    }

    .social-links a {
        width: 35px;
        height: 35px;
    }

    .footer-bottom {
        font-size: 0.8rem;
        padding-top: 1.5rem;
        margin-top: 1.5rem;
    }
}

/* Small Mobile Devices (321px to 375px) */
@media (min-width: 321px) and (max-width: 375px) {
    .container {
        padding: 0 12px;
    }

    .hero-content h1 {
        font-size: 1.6rem;
        line-height: 1.2;
    }

    .hero-content h2 {
        font-size: 1.3rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .section-header h2 {
        font-size: 1.7rem;
    }

    .service-card,
    .testimonial-card {
        padding: 1.2rem;
    }

    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .btn {
        padding: 0.75rem 1.2rem;
        font-size: 0.95rem;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 250px;
    }

    .marquee-content span {
        font-size: 0.85rem;
        padding: 0 0.8rem;
    }

    .quick-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .whatsapp-float {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
    }
}

/* Extra Small Devices (up to 320px) */
@media (max-width: 320px) {
    .container {
        padding: 0 10px;
    }

    .hero {
        padding-top: 70px;
        min-height: 90vh;
    }

    .hero-container {
        padding: 1rem 10px;
        gap: 1.5rem;
    }

    .hero-content h1 {
        font-size: 1.4rem;
        line-height: 1.2;
        margin-bottom: 0.8rem;
    }

    .hero-content h2 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .hero-content p {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }

    .section-header h2 {
        font-size: 1.5rem;
    }

    .section-header p {
        font-size: 1rem;
    }

    .service-card,
    .testimonial-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .service-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .service-card h3 {
        font-size: 1.2rem;
        margin-bottom: 0.8rem;
    }

    .btn {
        padding: 0.7rem 1rem;
        font-size: 0.9rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 0.8rem;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 220px;
    }

    .marquee-content span {
        font-size: 0.8rem;
        padding: 0 0.5rem;
    }

    .quick-actions {
        right: 5px;
        gap: 0.3rem;
    }

    .quick-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .whatsapp-float {
        bottom: 5px;
        left: 5px;
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .get-started-fixed {
        bottom: 5px;
        right: 5px;
    }

    .get-started-fixed .btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }

    .contact-form {
        padding: 1rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    .popup-content,
    .announcement-content {
        width: 95%;
        margin: 0.5rem;
    }

    .popup-header,
    .announcement-header {
        padding: 1rem;
    }

    .popup-header h2,
    .announcement-header h2 {
        font-size: 1.3rem;
    }

    .popup-body,
    .announcement-body {
        padding: 1rem;
    }

    .offer-item {
        flex-direction: column;
        text-align: center;
        padding: 0.8rem;
    }

    .offer-icon {
        font-size: 2rem;
        width: 50px;
        height: 50px;
    }

    .footer {
        padding: 1.5rem 0 0.8rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.2rem;
        text-align: center;
    }

    .footer-section {
        padding: 0 0.8rem;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 1rem;
        margin-bottom: 0.6rem;
    }

    .footer-section p {
        font-size: 0.85rem;
        line-height: 1.4;
        margin-bottom: 0.8rem;
    }

    .footer-section ul li {
        margin-bottom: 0.3rem;
    }

    .footer-section ul li a {
        font-size: 0.8rem;
        padding: 0.15rem 0;
        display: block;
    }

    .social-links {
        justify-content: center;
        gap: 0.6rem;
        margin-top: 1rem;
    }

    .social-links a {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .footer-bottom {
        font-size: 0.75rem;
        padding-top: 1rem;
        margin-top: 1rem;
        line-height: 1.3;
    }
}

/* Ultra Small Devices (up to 280px) */
@media (max-width: 280px) {
    .footer {
        padding: 1rem 0 0.5rem;
    }

    .footer-content {
        gap: 1rem;
    }

    .footer-section {
        padding: 0 0.5rem;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
    }

    .footer-section p {
        font-size: 0.8rem;
        line-height: 1.3;
        margin-bottom: 0.6rem;
    }

    .footer-section ul li {
        margin-bottom: 0.2rem;
    }

    .footer-section ul li a {
        font-size: 0.75rem;
        padding: 0.1rem 0;
    }

    .social-links {
        gap: 0.4rem;
        margin-top: 0.8rem;
    }

    .social-links a {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .footer-bottom {
        font-size: 0.7rem;
        padding-top: 0.8rem;
        margin-top: 0.8rem;
        line-height: 1.2;
    }
}

/* Offers Popup Styles */
.offers-popup,
.announcement-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.offers-popup.show,
.announcement-popup.show {
    opacity: 1;
    visibility: visible;
}

.popup-content,
.announcement-content {
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.offers-popup.show .popup-content,
.announcement-popup.show .announcement-content {
    transform: scale(1);
}

.popup-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 2rem;
    color: var(--text-light);
    cursor: pointer;
    z-index: 1;
    transition: var(--transition);
}

.popup-close:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

.popup-header,
.announcement-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 2rem;
    text-align: center;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.popup-header h2,
.announcement-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.8rem;
}

.popup-header p {
    margin: 0;
    opacity: 0.9;
}

.popup-body,
.announcement-body {
    padding: 2rem;
}

.offer-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border: 2px solid #f3f4f6;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    transition: var(--transition);
}

.offer-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.offer-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    color: var(--white);
}

.offer-details h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-dark);
    font-size: 1.2rem;
}

.original-price {
    text-decoration: line-through;
    color: var(--text-light);
    margin: 0;
    font-size: 0.9rem;
}

.offer-price {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0.25rem 0;
}

.offer-desc {
    color: var(--text-light);
    margin: 0;
    font-size: 0.9rem;
}

.popup-footer {
    background: var(--light-bg);
    padding: 1.5rem 2rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    text-align: center;
}

.offer-timer {
    color: var(--primary-color);
    font-weight: 600;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
}

.popup-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.announcement-body p {
    margin: 1rem 0;
    padding: 0.75rem;
    background: var(--light-bg);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.announcement-footer {
    padding: 1.5rem 2rem;
    text-align: center;
    background: var(--light-bg);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Popup Responsive */
@media (max-width: 768px) {
    .popup-content,
    .announcement-content {
        width: 95%;
        margin: 1rem;
    }

    .popup-header,
    .announcement-header,
    .popup-body,
    .announcement-body {
        padding: 1.5rem;
    }

    .popup-footer,
    .announcement-footer {
        padding: 1rem 1.5rem;
    }

    .offer-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .popup-buttons {
        flex-direction: column;
    }

    .popup-buttons .btn {
        width: 100%;
    }

    .marquee-content span {
        font-size: 1rem;
        padding: 0 2rem;
    }
}

/* Landscape Orientation for Mobile Devices */
@media (max-width: 767px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
        padding-top: 60px;
    }

    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        align-items: center;
    }

    .hero-content h1 {
        font-size: 1.8rem;
        line-height: 1.1;
        margin-bottom: 0.5rem;
    }

    .hero-content h2 {
        font-size: 1.3rem;
        margin-bottom: 0.8rem;
    }

    .hero-content p {
        font-size: 0.95rem;
        margin-bottom: 1rem;
    }

    .hero-buttons {
        flex-direction: row;
        gap: 0.8rem;
    }

    .hero-buttons .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .services {
        padding: 3rem 0;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .quick-actions {
        top: 40%;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn,
    .quick-btn,
    .whatsapp-float {
        min-height: 44px;
        min-width: 44px;
    }

    .nav-menu a {
        padding: 1rem;
        font-size: 1.1rem;
    }

    .service-card:hover,
    .testimonial-card:hover,
    .portfolio-item:hover {
        transform: none;
    }

    .service-card:active,
    .testimonial-card:active,
    .portfolio-item:active {
        transform: scale(0.98);
    }

    .btn:active {
        transform: scale(0.95);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-image img,
    .portfolio-item img,
    .testimonial-author img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Foldable Devices */
@media (max-width: 280px) {
    .container {
        padding: 0 8px;
    }

    .hero-content h1 {
        font-size: 1.2rem;
    }

    .hero-content h2 {
        font-size: 1rem;
    }

    .btn {
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
    }

    .service-card {
        padding: 0.8rem;
    }

    .service-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .marquee-content span {
        font-size: 0.7rem;
        padding: 0 0.3rem;
    }

    .quick-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .whatsapp-float {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

/* Large Screens (4K and above) */
@media (min-width: 1920px) {
    .container {
        max-width: 1600px;
    }

    .hero-content h1 {
        font-size: 4rem;
    }

    .hero-content h2 {
        font-size: 3rem;
    }

    .section-header h2 {
        font-size: 3.5rem;
    }

    .service-card {
        padding: 3.5rem;
    }

    .service-icon {
        width: 120px;
        height: 120px;
        font-size: 3rem;
    }

    .btn {
        padding: 1.2rem 2.5rem;
        font-size: 1.2rem;
    }

    .marquee-content span {
        font-size: 1.3rem;
        padding: 0 4rem;
    }
}
